# Emissions Factors Filter Analysis

## Current Implementation

### Frontend Query
The frontend makes requests to fetch emissions factors with the following parameters in `ActivityDatasetsCell.tsx`:
```typescript
const result = await refetchEmissionsFactors({
  activityName: searchValue.trim(),
  geography: geographyFilter,
  source: sourceFilter,
  unit: unitFilter
})
```

### GraphQL Schema
From `api/src/graphql/emissions_factors.sdl.ts`:
```graphql
type Query {
  getEmissionsFactors(
    activityName: String
    geography: [String]
    source: [String]
    unit: [String]
  ): [ActivityDatasetEmissionsFactors] @requireAuth
}
```

### API Endpoint
The request is ultimately sent to:
```
${LCA_API_ENDPOINT}/emissions-factors/${tenantID}/all
```

With parameters converted to comma-separated strings:
```typescript
params: {
  activity_name: sanitizeInput(activityName),
  geography: sanitizeInput(geography)?.join(','),
  source: sanitizeInput(source)?.join(','),
  unit: sanitizeInput(unit)?.join(','),
}
```

## Issue Description
When filtering emissions factors, the backend appears to be treating the filter values within each category (geography, source, unit) as OR conditions. This means that when "GLO" is selected as a geography filter, the results still include entries with other geographies like "RoW", "RER", etc.

### Example
Search term: "steel"
Geography filter: ["GLO"]
Current results include:
- Geography: GLO
- Geography: RoW
- Geography: RER
- etc.

Expected behavior: Only show results where geography exactly matches "GLO"

## Required Changes

### API Endpoint Changes
The backend API endpoint needs to be modified to:
1. Parse the comma-separated filter values properly
2. Apply each filter category (geography, source, unit) as an AND condition
3. Treat multiple values within each filter category as OR conditions

### Expected SQL Logic (Pseudocode)
```sql
SELECT * FROM emissions_factors
WHERE
  -- Basic activity name filter
  activity_name LIKE '%:activity_name%'

  -- Geography filter (OR within category)
  AND (geography IN (:geography_list))

  -- Source filter (OR within category)
  AND (source IN (:source_list))

  -- Unit filter (OR within category)
  AND (unit IN (:unit_list))
```

### API Response Contract
The response format should remain unchanged to maintain compatibility:
```typescript
type ActivityDatasetEmissionsFactors {
  efId: Int!
  activityName: String!
  description: String
  referenceProduct: String
  geography: String
  source: String
  unit: String
  isTenant: Boolean
}
```

## Testing Scenarios

### Basic Filtering
1. Single geography filter
2. Multiple geography filters
3. Combination of geography + source filters
4. Combination of geography + unit filters
5. All filters combined

### Edge Cases
1. Empty filter arrays
2. Invalid filter values
3. Case sensitivity in filter values
4. Special characters in filter values
5. Very large number of filter values

## Impact Analysis

### Affected Components
1. Frontend emissions factor search
2. Lab EF matching tool
3. Process model creation/editing
4. Custom emissions factor creation

### Performance Considerations
1. Index requirements for filtered columns
2. Query optimization for multiple OR conditions
3. Response time expectations
4. Pagination handling

## Migration Notes
1. Backend changes should be backward compatible
2. No frontend changes required
3. No database schema changes required
4. Consider adding API versioning if breaking changes are needed

## Endpoint Details

### URL
```
POST /emissions-factors/{tenantID}/all
```

### Request Headers
- `X-Tenant-ID`: Required for tenant-specific data
- `api-version`: Optional for versioning support

### Request Parameters
```typescript
{
  activity_name?: string,
  geography?: string[], // Comma-separated list
  source?: string[],    // Comma-separated list
  unit?: string[]       // Comma-separated list
}
```

### Success Response
```json
{
  "shared_emissions_factors": [...],
  "tenant_emissions_factors": [...]
}
```

### Error Response
```json
{
  "detail": "Error message"
}
```

## Additional Notes
- Consider adding support for exact/partial matching options
- Consider adding support for case-sensitive/insensitive matching
- Consider adding support for negative filters (NOT conditions)
- Consider adding support for wildcards in filter values
