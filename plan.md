# BOM Feature Access Control Plan

## Manufacturing Process Detection Analysis

1. **Current ML Inference Issues**:
   - Identifies as "Electronics Assembly" for cosmetic product
   - Suggests "electronic component production" for materials like water, glycerin

2. **Improvement Potential with Enhanced CSV Format**:
   - Use the "Function" field (Base/Solvent, Preservative, etc.) for better classification
   - Leverage ingredient types to determine product category
   - Create a more accurate manufacturing process prediction
   - Optionally include manufacturing process directly in the product header

3. **Example Data Insights from "Anti aging cream"**:
   - Water (deionized): Base/Solvent
   - Hyaluronic Acid: Hydration/Plumping Agent
   - Niacinamide: Brightening/Anti-Aging Agent
   - Phenoxyethanol: Preservative
   - These function descriptions provide rich context for proper manufacturing classification

## CSV Data Structure Analysis

1. **Current CSV Format**
   - Contains component-level information:
     - Component Name
     - Description
     - Amount
     - Unit of Measure
     - Vendor
     - Unit Price

2. **Missing Required Data**
   - Basic product information absent from CSV:
     - Product Name (currently derived from filename)
     - Product Category
     - Factory Location (currently defaulting to UK)
     - Target Market/Region (currently defaulting to UK)
   - Materials table missing fields:
     - CAS Numbers not in CSV
     - Component field could use Description from CSV
     - Supplier Origin could use Vendor from CSV
     - Need logic for Matched Activity
     - Need logic for Source field

3. **Default Values Handling**
   - Current approach uses UK as default for locations
   - Need to evaluate if we can:
     - Infer location from Vendor addresses
     - Use tenant's default location
     - Require manual input
     - Add location fields to CSV template

4. **Manufacturing Process Detection Issues**
   - Current ML inference is incorrect:
     - Identifies as "Electronics Assembly" for cosmetic product
     - Suggests "electronic component production" for materials like water, glycerin
   - Possible improvements needed:
     - Better manufacturing process inference based on materials
     - Consider ingredient types (chemicals, natural materials)
     - Use Description field hints (Base/Solvent, Humectant, etc.)
     - Possibly require manufacturing process in CSV

5. **Recommendations for ML Service**
   - Either:
     a. Modify CSV template to include basic product info section
     b. Keep current CSV format but require manual input of basic info
   - Current ML extraction works correctly for material names and quantities
   - Need to improve manufacturing process detection
   - Need to clarify if ML should attempt to infer product category
   - Consider using Description field content for better context

## Findings From Previous Implementation

1. **PDF/File Extraction Process**
   - Confirmed ML service extracted structured data including:
     - `product_name`, `product_id`, `annual_sales_volume`
     - `factory_city`, `factory_country`
     - Material `nodes` with properties (name, component, description, etc.)
   - Product category was not directly extracted but predicted via separate ML call (`predictProductCategory`)
   - Basic workflow: file → base64 → ML service → structured data → UI

2. **Manufacturing Process Detection**
   - Used a hybrid approach:
     - First fetched predefined manufacturing methods from LCA API
     - Then called ML prediction service (`predictManufacturingProcesses`)
     - Prediction based on product name AND category
   - Explains current issue: CSV lacks product category info needed for correct prediction
   - Code confirms manufacturing decisions happen after initial file extraction

3. **Default Values & Special Handling**
   - Human Scale tenants had special defaults in code:
   ```typescript
   if (orgMemberInfo?.orgMetadata?.processModelEnabled) {
     if (!productDetails.productName) productDetails.productName = 'Freedom Office Chair'
     // ... other defaults
   }
   ```
   - Target market locations were defaulted when not found in extracted data
   - Missing or "unknown" values were handled with fallbacks

## Current State Analysis

1. **Tenant Authorization**
   - Human Scale (with `processModelEnabled: true`):
     - Has full access to graph extraction
     - Can use parts/components import
     - Has CSV import capabilities
     - Uses headers `X-Use-Graph-Extraction: true` and `X-Parts-Only: true`

   - Other tenants (without `processModelEnabled`):
     - Currently limited to basic PDF extraction
     - Previously had no CSV or component import capabilities

2. **Key Implementation Points**
   - Main control point is in `api/src/services/product/extract_file.ts`
   - File type acceptance is configured in `web/src/components/AddProduct/AddProduct.tsx`
   - Process model handling in `api/src/services/product/product_process_model.ts`

## Proposed Changes

1. **Access Control Strategy**
   - Use inverse of `processModelEnabled` flag to identify tenants who should get BOM access
   - No new metadata fields needed
   - Formula: `BOM Access = !processModelEnabled`

2. **Implementation Areas**
   - File upload handlers
   - BOM processing logic
   - UI components visibility

3. **Impact Assessment**
   - Human Scale (processModelEnabled: true):
     - No changes to current workflow
     - Continues using existing graph extraction
     - Keeps current CSV capabilities

   - Other tenants (processModelEnabled: false):
     - Gain access to BOM feature
     - Maintain current PDF capabilities
     - Get structured data import via BOM

4. **Technical Considerations**
   - Need to ensure BOM processing doesn't interfere with graph extraction
   - Must maintain backward compatibility
   - Should preserve existing PDF processing

5. **Testing Requirements**
   - Verify Human Scale workflow remains unchanged
   - Confirm BOM works for non-Human Scale tenants
   - Test file type restrictions
   - Validate process model creation
   - Check authorization logic

## Risks and Mitigations

1. **Risks**
   - Potential overlap between BOM and graph extraction
   - Risk of breaking existing Human Scale features
   - Possibility of confusing UX with multiple import methods

2. **Mitigations**
   - Clear separation of processing paths
   - Comprehensive testing of Human Scale workflows
   - Clear UI indicators for available import methods

## PDF vs CSV Extraction Analysis

1. **PDF Extraction Success Factors**
   - The successful PDF extraction ("Vibrant Colors Laundry Plus") contained:
     - Rich contextual information about the product
     - Clear product name and code/ID at the top
     - Manufacturer address with city and country
     - Labeled "Bill of Materials" section
     - Component details with units and suppliers
     - Regulatory information suggesting product category

2. **Critical Missing Elements in CSV Processing**
   - **Product Category Detection**:
     - PDF extraction: Category is inferred from product name and regulatory info via `predictProductCategory`
     - Current CSV approach: Missing explicit category, failing to call `predictProductCategory` properly
     - Testing shows: Our improved CSV still shows "Other" as category despite clear cosmetics ingredients

   - **Materials Metadata (Matched Activity & Source)**:
     - PDF extraction: Properly identifies matched activities for materials (e.g., "fatty acid production")
     - Current CSV approach: With supplier location, showing some materials context but not complete
     - Testing confirms: Need to understand how material emissions factors are matched in PDF flow

3. **CSV vs PDF Comparison**
   - PDF advantages:
     - Provides complete product context (name, location, regulatory info)
     - Contains rich metadata that helps with categorization
     - Offers clear visual structure that ML model can interpret
     - Includes company information to determine geography
   - CSV limitations:
     - Only contains component-level data
     - Lacks product context (name, category, company info)
     - No regulatory information to help classify product type
     - Minimal structure for determining manufacturing processes

4. **Key Learnings from Latest Test**
   - Our improved CSV format with Function and Supplier Location dramatically improved:
     - Manufacturing process now correctly identified as "Cosmetic Manufacturing"
     - Material details include supplier locations
     - Material descriptions show up properly
   - Still needed:
     - Explicit product category field (critical for proper categorization)
     - Better source/activity matching for materials
     - Factory location from a header section (not inferred from suppliers)

## Implementation Recommendations

1. **BOM Feature Access Control**
   - Confirmed approach: Use inverse of `processModelEnabled` flag for BOM access
   - Human Scale continues with existing graph extraction workflow
   - Other tenants get new CSV BOM import feature

2. **CSV Template Enhancement**
   - Create a structured two-part CSV template based on the improved format seen in "Anti aging cream - Ingredients.csv":
     - **Section 1: Product Information Header Row**
       - Product Name: [e.g., "Anti-aging Cream"]
       - Product Code: [e.g., "SKU12345"]
       - Product Category: [e.g., "Cosmetics"]
       - Factory Location: [e.g., "Chicago, United States"]
       - Target Market: [e.g., "United States"]
       - Annual Sales Volume: [e.g., "1000"]
       - Manufacturing Process: [e.g., "Cold Process"]
       - Brand: [e.g., "NatureCare"]

     - **Section 2: Bill of Materials Table**
       - Column Headers:
         - Ingredient (renamed from Component Name)
         - Function (critical context - Base/Solvent, Preservative, etc.)
         - Supplier Name (separate field)
         - Supplier Location (separate field for geography)
         - Description (additional context)
         - Total Quantity (kg) (standardized unit)
         - Optional: CAS No. for chemical ingredients
         - Optional: Unit Cost

   - Advantages of this format:
     - "Function" field provides critical context for manufacturing detection
     - Separate supplier location field helps with geography inference
     - Standardized quantity units simplify processing
     - Clear indication of ingredient purpose helps categorization

3. **Manufacturing Process Prediction**
   - Leverage existing `predictManufacturingProcesses` function for BOM imports
   - Ensure product name and category are available for prediction
   - Allow direct specification of manufacturing process in CSV header
   - When manufacturing process isn't specified:
     - Use ingredient "Function" descriptions to infer product type
     - Group ingredients by function (e.g., "majority are preservatives/emulsifiers")
     - Compare against known patterns for different product categories
     - Use supplier information to narrow down manufacturing location

4. **Maintain Feature Parity**
   - Ensure BOM processing maintains parity with PDF extraction where appropriate
   - Reuse existing ML prediction calls for category and manufacturing
   - Preserve special handling for Human Scale tenants
   - Mirror the successful PDF extraction workflow:
     1. Extract basic product info from CSV header section
     2. Use product name to call `predictProductCategory` for category
     3. Use both name and category to call `predictManufacturingProcesses`
     4. Use component information for accurate material representation
     5. Apply appropriate defaults only when information is missing

5. **Critical Investigation Areas**
   - **Product Category Determination**:
     - Review how `predictProductCategory` is called in PDF workflow
     - Ensure CSV implementation makes identical call with proper parameters
     - Consider adding explicit category field to CSV to override prediction

   - **Material Matched Activity & Source**:
     - Investigate how PDF extraction identifies proper emissions factors
     - Determine if there's a separate ML call or database lookup
     - Examine if material function descriptions impact matched activities
     - Review how source database (Ecoinvent 3.11) is selected in PDF flow

## Next Steps

1. **Code Investigation**:
   - Examine `predictProductCategory` implementation to understand parameter requirements
   - Review how emissions factors are matched to materials in PDF extraction
   - Investigate source field determination mechanism in the materials view
   - Determine exact sequence of API calls made during PDF extraction

2. **Implementation path**:
   - Add authorization checks for BOM feature
   - Modify file upload handling
   - Update UI visibility based on tenant type
   - Ensure product category is explicitly defined in CSV or properly predicted
   - Implement correct materials metadata handling (matched activity & source)
   - Add validation and error handling
   - Comprehensive testing

3. **Validation**:
   - Test with both tenant types
   - Verify no regression in Human Scale features
   - Confirm proper BOM functionality for target tenants
   - Verify product category is correctly determined
   - Validate materials have proper matched activities and sources

## API Flow Analysis Findings

Based on our code investigation, we've identified the exact API flows for both product category determination and material metadata handling in the original codebase. These findings are critical to ensure our CSV implementation maintains feature parity.

### Product Category Determination

1. **API Flow Details**:
   - The `predictProductCategory` function takes only a single parameter: `productName`
   - It calls the LCA service endpoint: `GET /product-categories/predict-category`
   - This function is explicitly called after extracting the product name from the PDF

2. **Implementation Gap**:
   - Our CSV implementation is missing this critical call to `predictProductCategory`
   - We need to ensure that after extracting the product name from CSV, we explicitly call this prediction service
   - If we provide a product category field in our CSV template, it should override the prediction

### Material Metadata (Matched Activity & Source)

1. **API Flow Details**:
   - Material metadata is determined through the `getEmissionsFactors` function
   - This function calls: `GET /emissions-factors/{tenantID}/all`
   - It requires parameters like `activityName`, `geography`, `source`, and `unit`
   - The function combines shared and tenant-specific emissions factors

2. **Implementation Gap**:
   - We're not making this essential call in our CSV flow
   - The "Function" field in our improved CSV can map to the `activityName` parameter
   - Supplier location can provide the `geography` parameter

### Complete API Sequence for PDF Extraction

We've identified the complete sequence of API calls during PDF extraction:

1. **File Processing**: `POST /api/file-extraction/`
2. **Product Category Prediction**: `GET /product-categories/predict-category`
3. **Material Metadata Assignment**: `GET /emissions-factors/{tenantID}/all`
4. **Manufacturing Process Prediction**: Uses both product name AND category from steps 1 & 2

This confirms our hypothesis that CSV processing needs to follow the same sequence to achieve feature parity.

## Updated Implementation Plan

Based on these findings, we need to ensure our CSV implementation:

1. **For Product Category**:
   - Explicitly call `predictProductCategory` after extracting product name from CSV
   - This should happen before attempting manufacturing process prediction
   - Include an optional product category field in the CSV to override prediction when provided

2. **For Material Metadata**:
   - Call `getEmissionsFactors` for each material/component extracted from CSV
   - Use the "Function" field to provide the `activityName` parameter
   - Use supplier location to provide the `geography` parameter

3. **For Process Flow**:
   - Mirror the exact API call sequence identified in the PDF workflow
   - Ensure we maintain the data dependencies between calls (e.g., product category is needed for manufacturing process)

By implementing these changes, we can ensure full feature parity between CSV and PDF imports, particularly for product category prediction and material metadata handling, which were identified as the key missing elements in our testing.
