diff --git a/tests/e2e/02-01-inventory-add-product.spec.ts b/tests/e2e/02-01-inventory-add-product.spec.ts
index b7a770a..c9e111c 100644
--- a/tests/e2e/02-01-inventory-add-product.spec.ts
+++ b/tests/e2e/02-01-inventory-add-product.spec.ts
@@ -271,12 +271,12 @@ test('Add Product', async ({ page }) => {
 
   await page.getByRole('button', { name: 'edit', exact: true }).first().click();
 
-  await page.getByPlaceholder('Name').fill('mango');
+  await page.getByPlaceholder('Name').fill('citric acid');
 
   await page.click('button#search-ef-button');
-  await page.getByRole('row', { name: 'mango production' }).first().getByLabel('', { exact: true }).check();
+  await page.getByRole('row', { name: 'trichloroacetic acid' }).first().getByLabel('', { exact: true }).check();
   await page.getByRole('button', { name: 'Update' }).click();
-  await page.waitForSelector('text=mango production');
+  await page.waitForSelector('text=trichloroacetic acid');
 
   //Packaging
   //wait for packaging predictions
diff --git a/tests/e2e/02-02-inventory-add-product-from-file.spec.ts b/tests/e2e/02-02-inventory-add-product-from-file.spec.ts
index a55ae56..a3ef3dc 100644
--- a/tests/e2e/02-02-inventory-add-product-from-file.spec.ts
+++ b/tests/e2e/02-02-inventory-add-product-from-file.spec.ts
@@ -115,8 +115,6 @@ test('Add Product From File', async ({ page }) => {
 
   await page.click('button:has-text("Next")')
 
-  await page.waitForSelector('text=Manufacturing', { timeout: 10000 });
-
   await page.waitForSelector('text=Predicted', { timeout: 120000 });
 
   await page.click('button:has-text("Next")')
diff --git a/web/src/components/AddProduct/AddProduct.tsx b/web/src/components/AddProduct/AddProduct.tsx
index 29b5049..fa1fc57 100644
--- a/web/src/components/AddProduct/AddProduct.tsx
+++ b/web/src/components/AddProduct/AddProduct.tsx
@@ -896,6 +896,8 @@ const AddProduct = ({
     setAdvancedAddPackagingFieldsIsChecked,
   ] = useState(false)
 
+  const [importPartsProductCategory, setImportPartsProductCategory] = useState(null)
+
   const [
     predictIngredientEmissionsFactorsLoading,
     setPredictIngredientEmissionsFactorsLoading,
@@ -1084,6 +1086,17 @@ const AddProduct = ({
     }
   }, [productCategoryData, productCategoryDataError])
 
+  useEffect(() => {
+    // filter by pcr categories
+    if (productCategories && orgMemberInfo) {
+      const pcrCategories = Object.keys(orgMemberInfo?.orgMetadata?.pcr_categories ?? {})
+      if (pcrCategories.length) {
+        setProductCategories(productCategories.filter((category) => pcrCategories.includes(category.label)))
+      }
+    }
+
+  }, [productCategories, orgMemberInfo])
+
   useEffect(() => {
     if (rawMaterialsIngredientData) {
       const ingredients = rawMaterialsIngredientData.getRawMaterials.map(
@@ -2053,6 +2066,12 @@ const AddProduct = ({
       return
     }
 
+    if (!importPartsProductCategory?.trim()?.length) {
+      message.error('Please provide a category name')
+      return
+    }
+
+
     const _createProduct = async () => {
       const createProductInput = {
         product_id: Date.now().toString(),
@@ -2060,7 +2079,7 @@ const AddProduct = ({
         product_type: 'product',
         brand: orgName,
         country_of_use: 'United States',
-        primary_category: 'Office Chairs',
+        primary_category: importPartsProductCategory,
       }
 
       const response = await createProduct({
@@ -6145,7 +6164,7 @@ const AddProduct = ({
         content: (
           <>
             <p style={{ textAlign: 'left', fontSize: '16px' }}>
-              Components Overview
+              {component ? 'Component Info' : 'Product Info'}
             </p>
             <Form
               style={{ marginTop: '20px' }}
@@ -6167,8 +6186,37 @@ const AddProduct = ({
                   placeholder="Elegant Office Chair"
                 />
               </Form.Item>
+
+              {!component && (
+                <Form.Item
+                  label="Product Category"
+                  name="category"
+                  style={{ textAlign: 'left' }}
+                  rules={[{ required: true, message: 'Product category is required' }]}
+                  tooltip="Select a category from the list"
+                  >
+                  <Select
+                    showSearch
+                    allowClear
+                    options={productCategories}
+                    suffixIcon={null}
+                    style={{ textAlign: 'left' }}
+                    placeholder={productCategories.map((category) => category.label).join(', ')}
+                    filterOption={(inputValue, option) =>
+                      option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !==
+                      -1
+                    }
+                    onSelect={(value, option) => {
+                      setImportPartsProductCategory(value)
+                    }}
+                  />
+                </Form.Item>
+              )}
             </Form>
             <Divider />
+            <p style={{ textAlign: 'left', fontSize: '16px' }}>
+              Components Overview
+            </p>
             <DataTable
               key={addComponentTableKey}
               style={{
diff --git a/web/src/components/ProductInfoCell/ProductInfoCell.tsx b/web/src/components/ProductInfoCell/ProductInfoCell.tsx
index c1a6825..517d005 100644
--- a/web/src/components/ProductInfoCell/ProductInfoCell.tsx
+++ b/web/src/components/ProductInfoCell/ProductInfoCell.tsx
@@ -687,21 +687,6 @@ export const Success = ({
   const [pcrTableDataSource, setPcrTableDataSource] = useState({})
   const [pcrTableKey, setPcrTableKey] = useState(Date.now())
 
-  const [materialTableDataSourcePCR, setMaterialTableDataSourcePCR] = useState([])
-  const [materialTableKeyPCR, setMaterialTableKeyPCR] = useState(Date.now())
-  const [packagingTableDataSourcePCR, setPackagingTableDataSourcePCR] = useState([])
-  const [packagingTableKeyPCR, setPackagingTableKeyPCR] = useState(Date.now())
-  const [productionTableDataSourcePCR, setProductionTableDataSourcePCR] = useState([])
-  const [productionTableKeyPCR, setProductionTableKeyPCR] = useState(Date.now())
-  const [bundleTableDataSourcePCR, setBundleTableDataSourcePCR] = useState([])
-  const [bundleTableKeyPCR, setBundleTableKeyPCR] = useState(Date.now())
-  const [transportationTableDataSourcePCR, setTransportationTableDataSourcePCR] = useState([])
-  const [transportationTableKeyPCR, setTransportationTableKeyPCR] = useState(Date.now())
-  const [useTableDataSourcePCR, setUseTableDataSourcePCR] = useState([])
-  const [useTableKeyPCR, setUseTableKeyPCR] = useState(Date.now())
-  const [eolTableDataSourcePCR, setEolTableDataSourcePCR] = useState([])
-  const [eolTableKeyPCR, setEolTableKeyPCR] = useState(Date.now())
-
   const pcrNodeTypeMapping = {
     'Material Extraction': 'material',
     'Material Transformation': 'production',
@@ -712,99 +697,6 @@ export const Success = ({
     'Use': 'use',
   }
 
-  const pcrTableDataMap = {
-    'Material Extraction': materialTableDataSourcePCR,
-    'Material Transformation': productionTableDataSourcePCR,
-    'Assembly': bundleTableDataSourcePCR,
-    'Transport to Consumer': transportationTableDataSourcePCR,
-    'Material Transport': transportationTableDataSourcePCR,
-    'Waste Disposal': eolTableDataSourcePCR,
-    'Use': useTableDataSourcePCR,
-    }
-
-    const pcrTableKeyMap = {
-      'Material Extraction': materialTableKeyPCR,
-      'Material Transformation': productionTableKeyPCR,
-      'Assembly': bundleTableKeyPCR,
-      'Transport to Consumer': transportationTableKeyPCR,
-      'Material Transport': transportationTableKeyPCR,
-      'Waste Disposal': eolTableKeyPCR,
-      'Use': useTableKeyPCR,
-    }
-
-  const pcrLidTableDataSetters = {
-    material: {
-      data: setMaterialTableDataSourcePCR,
-      key: setMaterialTableKeyPCR,
-      src: materialTableDataSourcePCR,
-    },
-    packaging: {
-      data: setPackagingTableDataSourcePCR,
-      key: setPackagingTableKeyPCR,
-      src: packagingTableDataSourcePCR,
-    },
-    production: {
-      data: setProductionTableDataSourcePCR,
-      key: setProductionTableKeyPCR,
-      src: productionTableDataSourcePCR,
-    },
-    bundle: {
-      data: setBundleTableDataSourcePCR,
-      key: setBundleTableKeyPCR,
-      src: bundleTableDataSourcePCR,
-    },
-    transportation: {
-      data: setTransportationTableDataSourcePCR,
-      key: setTransportationTableKeyPCR,
-      src: transportationTableDataSourcePCR,
-    },
-    use: {
-      data: setUseTableDataSourcePCR,
-      key: setUseTableKeyPCR,
-      src: useTableDataSourcePCR,
-    },
-    eol: {
-      data: setEolTableDataSourcePCR,
-      key: setEolTableKeyPCR,
-      src: eolTableDataSourcePCR,
-    },
-  };
-
-  const setPCRLidTableData = (productInfo) => {
-
-    productInfo.pcrEmissions.forEach(segment => {
-      const emissionsData = segment.segmentEmissions.emissions.map(emission => {
-        const nodeInfo = productInfo.nodes.find(node => node.id === emission.id);
-        return {
-          ...emission,
-          inputAmount: nodeInfo.amount,
-          inputUnit: nodeInfo.unit,
-          quantity: nodeInfo.quantity,
-          component: nodeInfo.component,
-          nodeType: nodeInfo.nodeType,
-          segmentTotal: segment.segmentEmissions.totalEmissions,
-          scrapRate: nodeInfo.scrapRate,
-          scrapFate: nodeInfo.scrapFate,
-          segmentName: segment.segmentName,
-          percentage: (emission.totalEmissions / segment.segmentEmissions.totalEmissions) * 100,
-        };
-      });
-
-
-
-      const segmentType = pcrNodeTypeMapping[segment.segmentName];
-      if (!segmentType) {
-        message.error(`Invalid segment type: ${segment.segmentName}`);
-        throw new Error(`Invalid segment type: ${segment.segmentName}`);
-      }
-
-      pcrLidTableDataSetters[segmentType].data(emissionsData);
-      pcrLidTableDataSetters[segmentType].key(Date.now());
-
-    })
-
-  }
-
   useEffect(() => {
     setMaterialTableDataSource(productIngredientsData);
     setMaterialTableKey(Date.now());
@@ -818,11 +710,7 @@ export const Success = ({
     setUseTableKey(Date.now());
     setEolTableDataSource(productEolData);
     setEolTableKey(Date.now());
-
-    if (productInfo && pcrRule) {
-      setPCRLidTableData(productInfo)
-    }
-  }, [productInfo, pcrRule]);
+  }, [productInfo]);
 
   const [lcaReportType, setLcaReportType] = useState({
     type: 'word',
@@ -1685,11 +1573,10 @@ export const Success = ({
 
   const handleUpdateNode = async (node) => {
     try {
-      const _amount = parseFloat(node.amount) / (!calculateEmissionsPerUnitEnabled ? (productInfo.annualSalesVolumeUnits || 1) : 1);
       const updatePayload = {
         id: node.id,
         name: node.name,
-        amount: _amount,
+        amount: parseFloat(node.amount) / (!calculateEmissionsPerUnitEnabled ? (productInfo.annualSalesVolumeUnits || 1) : 1),
         unit: node.unit,
         component: node.component,
         description: node.description,
@@ -1722,8 +1609,7 @@ export const Success = ({
               ..._node,
               unsavedChanges: true,
               name: node.name,
-              inputAmount: node.amount,
-              amount: node.amount * parseInt(node.quantity),
+              amount: parseFloat(node.amount),
               unit: node.unit,
               component: node.component,
               description: node.description,
@@ -1737,11 +1623,13 @@ export const Success = ({
       };
 
       if (showPCREmissions) {
-
-        const updatedData = updateLocalData(pcrLidTableDataSetters[node.nodeType].src);
+        const updatedData = updateLocalData(pcrTableDataSource[node.nodeType]);
         if (updatedData.some(_node => _node.id === node.id)) {
-          pcrLidTableDataSetters[node.nodeType].data(updatedData);
-          pcrLidTableDataSetters[node.nodeType].key(Date.now());
+          setPcrTableDataSource({
+            ...pcrTableDataSource,
+            [node.nodeType]: updatedData
+          });
+          setPcrTableKey(Date.now());
         }
       } else {
         if (node.nodeType == 'material') {
@@ -2544,7 +2432,7 @@ export const Success = ({
       title: 'Icon',
       dataIndex: 'icon',
       render: (iconSrc, record) => (
-        <img style={{ width: '50px', height: '50px', objectFit: 'contain' }} src={iconSrc}></img>
+        <img src={iconSrc}></img>
       ),
       width: 60,
     },
@@ -3046,7 +2934,7 @@ export const Success = ({
       title: 'Total Amount',
       dataIndex: 'amount',
       key: 'amount',
-      render: (text, record) => `${formatFloat(text, 4) ?? 'N/A'} ${record.unit || ''}`,
+      render: (text, record) => `${formatFloat(text, 4)} ${record.unit}`,
       sorter: true,
       align: 'right',
     },
@@ -3113,6 +3001,22 @@ export const Success = ({
 
   const pcrAccordionItems = productInfo.pcrEmissions.map(segment => {
     const segmentPercentage = calculateSegmentPercentage(segment.segmentEmissions.totalEmissions);
+    const emissionsData = segment.segmentEmissions.emissions.map(emission => {
+      const nodeInfo = productInfo.nodes.find(node => node.id === emission.id);
+      return {
+        ...emission,
+        inputAmount: nodeInfo.amount,
+        inputUnit: nodeInfo.unit,
+        quantity: nodeInfo.quantity,
+        component: nodeInfo.component,
+        nodeType: nodeInfo.nodeType,
+        segmentTotal: segment.segmentEmissions.totalEmissions,
+        scrapRate: nodeInfo.scrapRate,
+        scrapFate: nodeInfo.scrapFate,
+        percentage: (emission.totalEmissions / segment.segmentEmissions.totalEmissions) * 100,
+      };
+    });
+
     let segmentColumns = [...pcrColumns];
     let materialColumns, productionColumns, eolColumns;
     if (segment.segmentName === "Material Transformation") {
@@ -3138,6 +3042,10 @@ export const Success = ({
           align: 'right',
         }
       );
+
+      const productionData = emissionsData.filter(item => item.nodeType === 'production');
+      const eolData = emissionsData.filter(item => item.nodeType === 'eol');
+
       const items = [
         {
           key: 'production',
@@ -3161,8 +3069,8 @@ export const Success = ({
               paginate={false}
               bordered
               columns={productionColumns}
-              data={productionTableDataSourcePCR.filter(item => item.nodeType === 'production')}
-              key={productionTableKeyPCR}
+              data={productionData}
+              key={`${pcrTableKey}-production`}
             />
           )
         },
@@ -3188,8 +3096,8 @@ export const Success = ({
               paginate={false}
               bordered
               columns={eolColumns}
-              data={productionTableDataSourcePCR.filter(item => item.nodeType === 'eol')}
-              key={`${productionTableKeyPCR}-${eolTableKeyPCR}`}
+              data={eolData}
+              key={`${pcrTableKey}-eol`}
             />
           )
         }
@@ -3237,7 +3145,7 @@ export const Success = ({
           title: 'Amount',
           dataIndex: 'inputAmount',
           key: 'inputAmount',
-          render: (text, record) => `${formatFloat(text, 2) ?? 'N/A'} ${record.inputUnit || ''}`,
+          render: (text, record) => `${formatFloat(text, 2)} ${record.inputUnit}`,
           sorter: true,
           width: 150,
           align: 'right',
@@ -3261,6 +3169,7 @@ export const Success = ({
         }
       );
 
+      const materialData = emissionsData.filter(item => ["material", "packaging"].includes(item.nodeType));
       return {
         key: segment.sequenceNo.toString(),
         label: (
@@ -3304,14 +3213,13 @@ export const Success = ({
               paginate={false}
               bordered
               columns={materialColumns}
-              data={materialTableDataSourcePCR.concat(packagingTableDataSourcePCR)}
-              key={`${materialTableKeyPCR}-${packagingTableKeyPCR}`}
+              data={materialData}
+              key={pcrTableKey}
             />
           </div>
         ),
       };
     }
-
     return {
       key: segment.sequenceNo.toString(),
       label: (
@@ -3355,8 +3263,8 @@ export const Success = ({
             paginate={false}
             bordered
             columns={segmentColumns}
-            data={pcrTableDataMap[segment.segmentName]}
-            key={pcrTableKeyMap[segment.segmentName]}
+            data={emissionsData}
+            key={pcrTableKey}
           />
         </div>
       ),
